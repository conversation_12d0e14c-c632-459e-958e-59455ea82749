#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译系统测试脚本
"""

import sys
import os
sys.path.append('test')

from simple_translator import SimpleTranslator

def test_translations():
    """测试翻译功能"""
    print("🔧 初始化翻译器...")
    translator = SimpleTranslator()

    # 测试文本1：鞋类产品描述
    test_text1 = """Premium leather running shoes with advanced cushioning technology.
    Features breathable mesh upper, durable rubber outsole, and ergonomic design
    for maximum comfort during long runs. Available in multiple colors and sizes."""

    # 测试文本2：简单产品特性
    test_text2 = """Waterproof hiking boots with anti-slip sole. Perfect for outdoor adventures."""

    # 测试文本3：商业描述
    test_text3 = """These comfortable sneakers are designed for daily wear. Made with high-quality materials and modern technology."""

    # 测试文本4：技术规格
    test_text4 = """Size: US 8-12. Material: Synthetic leather and mesh. Weight: 280g per shoe."""
    
    print("\n" + "="*60)
    print("📝 测试1：详细产品描述翻译")
    print("="*60)
    print(f"原文: {test_text1}")
    print()
    
    # 测试德语翻译
    print("🇩🇪 翻译到德语:")
    german_result = translator.translate(test_text1, "German")
    if german_result:
        print(f"✅ 成功: {german_result}")
    else:
        print("❌ 翻译失败")
    
    print("\n" + "-"*40)
    
    # 测试法语翻译
    print("🇫🇷 翻译到法语:")
    french_result = translator.translate(test_text1, "French")
    if french_result:
        print(f"✅ 成功: {french_result}")
    else:
        print("❌ 翻译失败")
    
    print("\n" + "="*60)
    print("📝 测试2：简短产品描述翻译")
    print("="*60)
    print(f"原文: {test_text2}")
    print()
    
    # 测试西班牙语翻译
    print("🇪🇸 翻译到西班牙语:")
    spanish_result = translator.translate(test_text2, "Spanish")
    if spanish_result:
        print(f"✅ 成功: {spanish_result}")
    else:
        print("❌ 翻译失败")
    
    print("\n" + "-"*40)
    
    # 测试意大利语翻译
    print("🇮🇹 翻译到意大利语:")
    italian_result = translator.translate(test_text2, "Italian")
    if italian_result:
        print(f"✅ 成功: {italian_result}")
    else:
        print("❌ 翻译失败")
    
    print("\n" + "="*60)
    print("📝 测试3：商业描述翻译")
    print("="*60)
    print(f"原文: {test_text3}")
    print()

    # 测试德语翻译
    print("🇩🇪 翻译到德语:")
    german_result3 = translator.translate(test_text3, "German")
    if german_result3:
        print(f"✅ 成功: {german_result3}")
    else:
        print("❌ 翻译失败")

    print("\n" + "-"*40)

    # 测试法语翻译
    print("🇫🇷 翻译到法语:")
    french_result3 = translator.translate(test_text3, "French")
    if french_result3:
        print(f"✅ 成功: {french_result3}")
    else:
        print("❌ 翻译失败")

    print("\n" + "="*60)
    print("📝 测试4：技术规格翻译")
    print("="*60)
    print(f"原文: {test_text4}")
    print()

    # 测试西班牙语翻译
    print("🇪🇸 翻译到西班牙语:")
    spanish_result4 = translator.translate(test_text4, "Spanish")
    if spanish_result4:
        print(f"✅ 成功: {spanish_result4}")
    else:
        print("❌ 翻译失败")

    print("\n" + "-"*40)

    # 测试意大利语翻译
    print("🇮🇹 翻译到意大利语:")
    italian_result4 = translator.translate(test_text4, "Italian")
    if italian_result4:
        print(f"✅ 成功: {italian_result4}")
    else:
        print("❌ 翻译失败")

    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)

    results = [
        ("德语-测试1", german_result),
        ("法语-测试1", french_result),
        ("西班牙语-测试2", spanish_result),
        ("意大利语-测试2", italian_result),
        ("德语-测试3", german_result3),
        ("法语-测试3", french_result3),
        ("西班牙语-测试4", spanish_result4),
        ("意大利语-测试4", italian_result4)
    ]

    success_count = sum(1 for _, result in results if result)
    total_count = len(results)

    print(f"成功翻译: {success_count}/{total_count}")

    for lang, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{lang}: {status}")

    if success_count == total_count:
        print("\n🎉 所有翻译测试通过！")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个翻译失败")

if __name__ == "__main__":
    test_translations()
