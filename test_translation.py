#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译系统测试脚本
"""

import sys
import os
sys.path.append('test')

from simple_translator import SimpleTranslator

def test_translations():
    """测试翻译功能"""
    print("🔧 初始化翻译器...")
    translator = SimpleTranslator()
    
    # 测试文本1：鞋类产品描述
    test_text1 = """Premium leather running shoes with advanced cushioning technology. 
    Features breathable mesh upper, durable rubber outsole, and ergonomic design 
    for maximum comfort during long runs. Available in multiple colors and sizes."""
    
    # 测试文本2：简单产品特性
    test_text2 = """Waterproof hiking boots with anti-slip sole. Perfect for outdoor adventures."""
    
    print("\n" + "="*60)
    print("📝 测试1：详细产品描述翻译")
    print("="*60)
    print(f"原文: {test_text1}")
    print()
    
    # 测试德语翻译
    print("🇩🇪 翻译到德语:")
    german_result = translator.translate(test_text1, "German")
    if german_result:
        print(f"✅ 成功: {german_result}")
    else:
        print("❌ 翻译失败")
    
    print("\n" + "-"*40)
    
    # 测试法语翻译
    print("🇫🇷 翻译到法语:")
    french_result = translator.translate(test_text1, "French")
    if french_result:
        print(f"✅ 成功: {french_result}")
    else:
        print("❌ 翻译失败")
    
    print("\n" + "="*60)
    print("📝 测试2：简短产品描述翻译")
    print("="*60)
    print(f"原文: {test_text2}")
    print()
    
    # 测试西班牙语翻译
    print("🇪🇸 翻译到西班牙语:")
    spanish_result = translator.translate(test_text2, "Spanish")
    if spanish_result:
        print(f"✅ 成功: {spanish_result}")
    else:
        print("❌ 翻译失败")
    
    print("\n" + "-"*40)
    
    # 测试意大利语翻译
    print("🇮🇹 翻译到意大利语:")
    italian_result = translator.translate(test_text2, "Italian")
    if italian_result:
        print(f"✅ 成功: {italian_result}")
    else:
        print("❌ 翻译失败")
    
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    
    results = [
        ("德语", german_result),
        ("法语", french_result), 
        ("西班牙语", spanish_result),
        ("意大利语", italian_result)
    ]
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"成功翻译: {success_count}/{total_count}")
    
    for lang, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{lang}: {status}")
    
    if success_count == total_count:
        print("\n🎉 所有翻译测试通过！")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个翻译失败")

if __name__ == "__main__":
    test_translations()
