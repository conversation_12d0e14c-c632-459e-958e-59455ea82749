#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量翻译测试
"""

import sys
import os
sys.path.append('test')

from simple_translator import SimpleTranslator

def test_batch_translation():
    """测试批量翻译功能"""
    print("🔧 初始化翻译器...")
    translator = SimpleTranslator()
    
    # 测试不同类型的文本
    test_texts = [
        "Comfortable running shoes with excellent grip.",
        "Premium quality leather boots for hiking.",
        "Lightweight sneakers perfect for everyday use."
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n{'='*60}")
        print(f"📝 批量翻译测试 {i}")
        print("="*60)
        print(f"原文: {text}")
        print()
        
        # 执行批量翻译
        results = translator.batch_translate(text)
        
        print("翻译结果:")
        for lang, translation in results.items():
            if translation:
                print(f"🇩🇪 {lang}: ✅ {translation[:100]}{'...' if len(translation) > 100 else ''}")
            else:
                print(f"❌ {lang}: 翻译失败")
        
        # 保存结果
        filename = f"batch_results_{i}.json"
        translator.save_results(results, filename)

if __name__ == "__main__":
    test_batch_translation()
