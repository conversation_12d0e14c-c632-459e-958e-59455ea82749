#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极简电商翻译系统
================

专门用于鞋类产品的多语言翻译，基于OpenRouter API。
核心功能：单语言翻译、批量翻译、智能模型选择。
"""

import requests
import json
from typing import Optional, Dict, List

# 配置
API_KEY = "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"  # 请替换为您的API密钥
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"

class SimpleTranslator:
    """极简翻译器"""
    
    # 语言对应的最优模型
    MODELS = {
        'german': "openai/chatgpt-4o-latest",
        'spanish': "openai/chatgpt-4o-latest", 
        'italian': "openai/chatgpt-4o-latest",
        'french': "google/gemini-2.5-pro"
    }
    
    # 翻译提示模板
    PROMPT = """E-commerce Translation Task for Footwear Category:
Translate the following {origin} text into {target} for Amazon marketplace listings.

## Translation Requirements:
### Language Quality Standards:
- Provide natural, native-level {target} translation
- Use terminology appropriate for footwear/fashion industry
- Follow {target} e-commerce conventions and consumer expectations
- Ensure grammatical accuracy and proper spelling
- Match the tone and style suitable for product descriptions

### Content Preservation:
- Maintain all original meaning, technical specifications, and selling points
- Preserve product features, benefits, and key attributes
- Keep measurement units, sizes, and technical terms accurate
- Retain brand positioning and marketing appeal

### E-commerce Optimization:
- Use consumer-friendly language that drives purchasing decisions
- Employ terminology familiar to {target} online shoppers
- Ensure translation supports product searchability and conversion
- Avoid overly technical or medical terminology unless product-specific

### Universal Quality Standards:
- Avoid literal translations that sound unnatural to native speakers
- Use appropriate register for commercial/e-commerce context (avoid overly formal, literary, or technical language unless product-specific)
- Ensure grammatical accuracy including:
  * Gender/case agreement where applicable
  * Correct preposition usage
  * Proper spelling and spacing
  * Appropriate punctuation and capitalization
- Select terminology appropriate for the product category (avoid medical/religious terms for everyday products)
- Match cultural expectations and shopping behavior patterns of target market

### Language-Specific Refinements:
**German:** Standard commercial register; "Komfort" not "Linderung" for comfort features; proper compound noun capitalization
**French:** Gender agreement (beau/belle); natural contractions (du, au); e-commerce appropriate formality level
**English:** American spelling for US market; natural phrasing over literal translation; industry-standard terminology

### Output Format:
Provide only the translated text without explanations, comments, or additional formatting.

Text to translate:
###
{text}
###"""

    def __init__(self, api_key: str = API_KEY):
        """初始化翻译器"""
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def translate(self, text: str, target: str = "German", origin: str = "English") -> Optional[str]:
        """
        翻译文本
        
        Args:
            text: 待翻译文本
            target: 目标语言 (German/French/Spanish/Italian)
            origin: 源语言 (默认English)
            
        Returns:
            翻译结果或None
        """
        try:
            # 选择模型
            model = self.MODELS.get(target.lower(), "google/gemini-2.5-pro")
            
            # 创建提示
            prompt = self.PROMPT.format(
                origin=origin,
                target=target, 
                text=text
            )
            
            # API请求
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.3
            }
            
            response = requests.post(OPENROUTER_URL, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                print(f"翻译失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"错误: {e}")
            return None
    
    def batch_translate(self, text: str, targets: List[str] = None) -> Dict[str, str]:
        """
        批量翻译
        
        Args:
            text: 待翻译文本
            targets: 目标语言列表
            
        Returns:
            语言->翻译结果的字典
        """
        if targets is None:
            targets = ["German", "French", "Spanish", "Italian"]
        
        results = {}
        for target in targets:
            print(f"翻译到 {target}...")
            translation = self.translate(text, target)
            if translation:
                results[target] = translation
        
        return results
    
    def save_results(self, results: Dict[str, str], filename: str = "translations.json"):
        """保存翻译结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {filename}")
        except Exception as e:
            print(f"保存失败: {e}")

if __name__ == "__main__":
    # 基本使用示例
    translator = SimpleTranslator()

    # 使用方法:
    result = translator.translate("Your text here", "German")
    batch_results = translator.batch_translate("Your text here")
    translator.save_results(batch_results)

    print("SimpleTranslator initialized. Use translate() or batch_translate() methods.")
