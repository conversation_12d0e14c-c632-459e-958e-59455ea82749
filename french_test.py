#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
法语翻译修复测试
"""

import sys
import os
sys.path.append('test')

from simple_translator import SimpleTranslator

def test_french_translation():
    """测试修复后的法语翻译"""
    print("🔧 初始化翻译器...")
    translator = SimpleTranslator()
    
    test_texts = [
        "Comfortable running shoes with excellent grip.",
        "Premium quality leather boots for hiking.",
        "Lightweight sneakers perfect for everyday use."
    ]
    
    print("🇫🇷 测试法语翻译修复...")
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n测试 {i}: {text}")
        result = translator.translate(text, "French")
        if result:
            print(f"✅ 成功: {result}")
        else:
            print("❌ 仍然失败")

if __name__ == "__main__":
    test_french_translation()
